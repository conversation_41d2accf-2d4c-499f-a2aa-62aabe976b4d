# -*- coding: utf-8 -*-
"""Untitled144.ipynb

Automatically generated by <PERSON><PERSON>.

Original file is located at
    https://colab.research.google.com/drive/1tZRnyQsQHmcw3YzDH1j_AUuO6UsSkcSs
"""

import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()
import yaml
import getpass
from typing import TypedDict, List, Dict, Any
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import create_react_agent
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_community.tools.tavily_search import TavilySearchResults
from langchain.tools import tool
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_experimental.tools import PythonREPLTool
import ast
import re
# Remove IPython dependencies for non-notebook environment
# from IPython.display import Image, display
from langchain_core.runnables.graph import MermaidDrawMethod




# Environment setup - keys are now loaded from .env file

"""
Self-Documenting Code Analysis Agent System

This system uses multiple specialized agents to analyze, document, and validate Python code.
Each agent has a specific role and uses tailored prompts for optimal performance.
"""

import os
import yaml
import getpass
from typing import TypedDict, List, Dict, Any
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import create_react_agent
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_community.tools.tavily_search import TavilySearchResults
from langchain.tools import tool
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_experimental.tools import PythonREPLTool
import ast
import re
# Remove IPython dependencies for non-notebook environment

# Environment Setup
def setup_environment():
    """Set up API keys for the agents"""
    if "GOOGLE_API_KEY" not in os.environ:
        os.environ["GOOGLE_API_KEY"] = getpass.getpass("Enter your Google API Key: ")

    if "TAVILY_API_KEY" not in os.environ:
        os.environ["TAVILY_API_KEY"] = getpass.getpass("Enter your Tavily API Key: ")

# State Definition
class DocumentationState(TypedDict):
    """
    Shared state between all agents in the workflow

    Contains:
    - Code being processed
    - Analysis results
    - Documentation status
    - Generated outputs
    """
    original_code: str
    current_code: str
    documentation_quality: str
    libraries_used: List[str]
    issues_found: List[str]
    sample_runs: List[Dict[str, Any]]
    final_output: str
    current_step: str

# Tools Definition
@tool
def analyze_code_structure(code: str) -> str:
    """
    Analyze Python code to extract imports, functions, and classes

    Args:
        code: Python code as string

    Returns:
        Formatted analysis of code structure
    """
    tree = ast.parse(code)

    imports = []
    functions = []
    classes = []

    for node in ast.walk(tree):
        if isinstance(node, ast.Import):
            for alias in node.names:
                imports.append(alias.name)
        elif isinstance(node, ast.ImportFrom):
            module = node.module or ""
            for alias in node.names:
                imports.append(f"{module}.{alias.name}")
        elif isinstance(node, ast.FunctionDef):
            functions.append(node.name)
        elif isinstance(node, ast.ClassDef):
            classes.append(node.name)

    return f"Imports: {imports}, Functions: {functions}, Classes: {classes}"

@tool
def search_library_info(library_name: str) -> str:
    """
    Search for library documentation and usage examples

    Args:
        library_name: Name of the Python library

    Returns:
        Documentation information about the library
    """
    search_tool = TavilySearchResults(max_results=2)
    query = f"{library_name} python library documentation examples"
    results = search_tool.invoke(query)

    formatted_results = []
    for result in results:
        content = result.get('content', 'No content')[:200]
        formatted_results.append(f"Source: {result.get('url', 'N/A')}\nContent: {content}...")

    return "\n---\n".join(formatted_results)

# Agent Prompts
ANALYZER_PROMPT = """
You are a Code Structure Analyzer. Your job is to:
1. Analyze Python code structure
2. Identify all imported libraries
3. Assess current documentation quality
4. Determine what libraries need research

Be thorough but concise. Focus on factual analysis.
"""

RESEARCHER_PROMPT = """
You are a Library Research Specialist. Your job is to:
1. Research unfamiliar libraries found in code
2. Find documentation and usage patterns
3. Provide context for better documentation

Focus on practical usage information that helps with documentation.
"""

DOCUMENTER_PROMPT = """
You are a Documentation Generator. Your job is to:
1. Add comprehensive docstrings to functions and classes
2. Add helpful comments for complex logic
3. Maintain original code functionality
4. Follow Python documentation best practices

Rules:
- Use triple quotes for docstrings
- Include parameter and return value descriptions
- Add single-line comments for complex logic
- Keep existing code unchanged
"""

VALIDATOR_PROMPT = """
You are a Code Validator and Tester. Your job is to:
1. Find potential issues in code
2. Identify syntax errors or logic problems
3. Create sample test runs with realistic inputs
4. Suggest improvements

Be practical and focus on common issues developers face.
"""

# Initialize Models and Agents
def create_agents():
    """Create specialized agents for each task"""
    model = ChatGoogleGenerativeAI(
        model="gemini-2.5-flash",
        temperature=0.3,
        google_api_key=os.environ["GOOGLE_API_KEY"]
    )

    # Tools for each agent
    analysis_tools = [analyze_code_structure]
    research_tools = [search_library_info]

    # Create specialized agents with different prompts
    analyzer_agent = create_react_agent(
        model=model,
        tools=analysis_tools,
        prompt=ChatPromptTemplate.from_messages([
            ("system", ANALYZER_PROMPT),
            ("placeholder", "{messages}")
        ])
    )

    researcher_agent = create_react_agent(
        model=model,
        tools=research_tools,
        prompt=ChatPromptTemplate.from_messages([
            ("system", RESEARCHER_PROMPT),
            ("placeholder", "{messages}")
        ])
    )

    documenter_agent = create_react_agent(
        model=model,
        tools=[],
        prompt=ChatPromptTemplate.from_messages([
            ("system", DOCUMENTER_PROMPT),
            ("placeholder", "{messages}")
        ])
    )

    validator_agent = create_react_agent(
        model=model,
        tools=[],
        prompt=ChatPromptTemplate.from_messages([
            ("system", VALIDATOR_PROMPT),
            ("placeholder", "{messages}")
        ])
    )

    return analyzer_agent, researcher_agent, documenter_agent, validator_agent

# Workflow Nodes
def analysis_node(state: DocumentationState) -> DocumentationState:
    """
    Analyze code structure and determine documentation needs

    Uses the analyzer agent to examine code and extract key information
    """
    analyzer_agent, _, _, _ = create_agents()

    # Analyze the code
    analysis_input = {
        "messages": [HumanMessage(content=f"Analyze this Python code structure:\n\n{state['original_code']}")]
    }

    result = analyzer_agent.invoke(analysis_input)
    response_text = result["messages"][-1].content

    # Extract libraries (simple pattern matching)
    libraries = []
    for line in state['original_code'].split('\n'):
        if line.strip().startswith(('import ', 'from ')):
            if 'import' in line:
                lib = line.split('import')[1].strip().split()[0].split('.')[0]
                libraries.append(lib)

    # Check documentation quality
    has_docstrings = '"""' in state['original_code'] or "'''" in state['original_code']
    has_comments = '#' in state['original_code']

    if has_docstrings and has_comments:
        quality = "good"
    elif has_docstrings or has_comments:
        quality = "partial"
    else:
        quality = "missing"

    return {
        **state,
        "current_code": state['original_code'],
        "documentation_quality": quality,
        "libraries_used": libraries,
        "current_step": "analyzed"
    }

def research_node(state: DocumentationState) -> DocumentationState:
    """
    Research libraries used in the code for better context

    Uses the researcher agent to gather information about imported libraries
    """
    _, researcher_agent, _, _ = create_agents()

    # Research non-standard libraries
    standard_libs = ['os', 'sys', 're', 'json', 'math', 'datetime', 'collections']
    libraries_to_research = [lib for lib in state['libraries_used'] if lib not in standard_libs]

    research_info = ""
    for library in libraries_to_research[:3]:  # Limit to 3 libraries
        research_input = {
            "messages": [HumanMessage(content=f"Research the {library} library and provide key usage information")]
        }

        result = researcher_agent.invoke(research_input)
        research_info += f"\n{library}: {result['messages'][-1].content[:200]}...\n"

    return {
        **state,
        "current_step": "researched"
    }

def documentation_node(state: DocumentationState) -> DocumentationState:
    """
    Generate comprehensive documentation for the code

    Uses the documenter agent to add docstrings and comments
    """
    _, _, documenter_agent, _ = create_agents()

    doc_input = {
        "messages": [HumanMessage(content=f"""
        Add comprehensive documentation to this Python code:

        {state['current_code']}

        Libraries used: {', '.join(state['libraries_used'])}

        Add docstrings and comments. Return only the documented code.
        """)]
    }

    result = documenter_agent.invoke(doc_input)
    documented_code = result["messages"][-1].content

    # Clean up the response to extract just the code
    if "```python" in documented_code:
        documented_code = documented_code.split("```python")[1].split("```")[0].strip()
    elif "```" in documented_code:
        documented_code = documented_code.split("```")[1].split("```")[0].strip()

    return {
        **state,
        "current_code": documented_code,
        "current_step": "documented"
    }

def validation_node(state: DocumentationState) -> DocumentationState:
    """
    Validate code and generate test samples

    Uses the validator agent to find issues and create sample runs
    """
    _, _, _, validator_agent = create_agents()

    # Find issues
    issue_input = {
        "messages": [HumanMessage(content=f"""
        Analyze this code for potential issues:

        {state['current_code']}

        List any syntax errors, logic issues, or improvements needed.
        """)]
    }

    issue_result = validator_agent.invoke(issue_input)
    issues_text = issue_result["messages"][-1].content

    # Extract issues (simple parsing)
    issues = [line.strip() for line in issues_text.split('\n') if line.strip() and
              any(keyword in line.lower() for keyword in ['issue', 'error', 'problem', 'warning'])]

    # Generate sample runs
    sample_input = {
        "messages": [HumanMessage(content=f"""
        Create 2 sample test runs for this code:

        {state['current_code']}

        Format each as: Input: [values] | Expected: [result]
        """)]
    }

    sample_result = validator_agent.invoke(sample_input)
    sample_text = sample_result["messages"][-1].content

    # Parse sample runs
    sample_runs = []
    for line in sample_text.split('\n'):
        if 'Input:' in line and ('Expected:' in line or 'Output:' in line):
            parts = line.split('|')
            if len(parts) >= 2:
                input_part = parts[0].replace('Input:', '').strip()
                output_part = parts[1].replace('Expected:', '').replace('Output:', '').strip()
                sample_runs.append({"input": input_part, "expected": output_part})

    return {
        **state,
        "issues_found": issues,
        "sample_runs": sample_runs,
        "current_step": "validated"
    }

def output_node(state: DocumentationState) -> DocumentationState:
    """
    Generate final comprehensive output

    Combines all analysis results into a formatted report
    """
    # Create final output with all information
    output = f"""# Code Documentation Report

## Libraries Used
{', '.join(state['libraries_used'])}

## Documentation Quality
{state['documentation_quality']}

## Documented Code
```python
{state['current_code']}
```

## Issues Found
{chr(10).join([f"- {issue}" for issue in state['issues_found']]) if state['issues_found'] else "No issues found"}

## Sample Test Runs
{chr(10).join([f"**Test {i+1}:** {sample['input']} → {sample['expected']}"
               for i, sample in enumerate(state['sample_runs'])]) if state['sample_runs'] else "No sample runs generated"}

---
*Generated by LangGraph Documentation Agent System*
"""

    return {
        **state,
        "final_output": output,
        "current_step": "completed"
    }

# Workflow Creation
def create_documentation_workflow():
    """
    Create and configure the documentation workflow

    Returns:
        Compiled LangGraph workflow for code documentation
    """
    workflow = StateGraph(DocumentationState)

    # Add all nodes to the workflow
    workflow.add_node("analyze", analysis_node)
    workflow.add_node("research", research_node)
    workflow.add_node("document", documentation_node)
    workflow.add_node("validate", validation_node)
    workflow.add_node("output", output_node)

    # Define the flow: analyze → research → document → validate → output
    workflow.add_edge("analyze", "research")
    workflow.add_edge("research", "document")
    workflow.add_edge("document", "validate")
    workflow.add_edge("validate", "output")
    workflow.add_edge("output", END)

    # Set entry point
    workflow.set_entry_point("analyze")

    # Compile the workflow first
    compiled_workflow = workflow.compile()

    # Save workflow visualization as PNG
    try:
        # Try using Pyppeteer for local rendering
        graph_png = compiled_workflow.get_graph().draw_mermaid_png(
            draw_method=MermaidDrawMethod.PYPPETEER
        )
        with open("workflow_diagram.png", "wb") as f:
            f.write(graph_png)
        print("Workflow diagram saved as workflow_diagram.png")
    except Exception as e:
        print(f"Could not save workflow diagram: {e}")
        # Try alternative method without draw_method
        try:
            graph_png = compiled_workflow.get_graph().draw_mermaid_png()
            with open("workflow_diagram.png", "wb") as f:
                f.write(graph_png)
            print("Workflow diagram saved as workflow_diagram.png (fallback method)")
        except Exception as e2:
            print(f"Fallback method also failed: {e2}")

    return compiled_workflow

def display_workflow():
    """Display the workflow graph visualization"""
    workflow = create_documentation_workflow()
    try:
        graph_png = workflow.get_graph().draw_mermaid_png()
        with open("workflow_diagram.png", "wb") as f:
            f.write(graph_png)
        print("Workflow diagram saved as workflow_diagram.png")
    except Exception as e:
        print(f"Could not save workflow diagram: {e}")

def run_documentation_agent(code_input: str) -> Dict:
    """
    Run the complete documentation workflow on provided code

    Args:
        code_input: Python code to analyze and document

    Returns:
        Dictionary containing all results from the workflow
    """
    # Set up environment
    setup_environment()

    # Create and run workflow
    app = create_documentation_workflow()

    # Initialize state
    initial_state = {
        "original_code": code_input,
        "current_code": code_input,
        "documentation_quality": "",
        "libraries_used": [],
        "issues_found": [],
        "sample_runs": [],
        "final_output": "",
        "current_step": "start"
    }

    # Execute workflow
    result = app.invoke(initial_state)

    # Display results
    print("=" * 60)
    print("DOCUMENTATION WORKFLOW COMPLETED")
    print("=" * 60)
    print(f"Final Step: {result['current_step']}")
    print(f"Libraries Found: {len(result['libraries_used'])}")
    print(f"Issues Identified: {len(result['issues_found'])}")
    print(f"Sample Tests: {len(result['sample_runs'])}")
    print("\n" + result['final_output'])

    return result

def write_code_to_file(filename: str):
    """
    Write the current script to a file

    Args:
        filename: Name of the file to write to
    """
    try:
        # Read the current script file
        with open(__file__, 'r', encoding='utf-8') as f:
            current_code = f.read()

        # Write to the specified file
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(current_code)

        print(f"Current script saved to {filename}")
    except Exception as e:
        print(f"Error saving script to {filename}: {e}")

# Example Usage
if __name__ == "__main__":
    # Display workflow visualization
    print("Documentation Agent Workflow:")
    #display_workflow()

    # Sample code to process
    sample_code = """
import pandas as pd
import numpy as np

def process_data(data, threshold=0.5):
    filtered = data[data > threshold]
    return filtered.mean()

class DataAnalyzer:
    def __init__(self, data):
        self.data = data

    def analyze(self):
        result = process_data(self.data)
        return result * 2

data = np.array([0.1, 0.7, 0.9, 0.3, 0.8])
analyzer = DataAnalyzer(data)
print(analyzer.analyze())
"""

    # Run the documentation agent
    result = run_documentation_agent(sample_code)

    # Write current code to file
    write_code_to_file("code.py")
    #print("Current script saved to code.py")

