# Self-Documentation Agent System

A sophisticated multi-agent system built with <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> that automatically analyzes, researches, documents, and validates Python code. This system uses specialized AI agents to provide comprehensive code documentation and analysis.

## 🚀 Features

- **Multi-Agent Architecture**: Specialized agents for different tasks (Analysis, Research, Documentation, Validation)
- **Automated Code Analysis**: Identifies libraries, structure, and documentation quality
- **Library Research**: Automatically researches unfamiliar libraries using web search
- **Intelligent Documentation**: Generates comprehensive docstrings and comments
- **Code Validation**: Identifies potential issues and suggests improvements
- **Workflow Visualization**: Generates visual representations of the agent workflow

## 📋 Workflow

The system follows a structured workflow with the following stages:

```mermaid
---
config:
  flowchart:
    curve: linear
---
graph TD;
	__start__([<p>__start__</p>]):::first
	analyze(analyze)
	research(research)
	document(document)
	validate(validate)
	output(output)
	__end__([<p>__end__</p>]):::last
	__start__ --> analyze;
	analyze --> research;
	document --> validate;
	research --> document;
	validate --> output;
	output --> __end__;
	classDef default fill:#f2f0ff,line-height:1.2
	classDef first fill-opacity:0
	classDef last fill:#bfb6fc
```

### Agent Roles

1. **Analyzer Agent**: Analyzes code structure, identifies libraries, and assesses documentation quality
2. **Researcher Agent**: Researches unfamiliar libraries and provides context for documentation
3. **Documenter Agent**: Generates comprehensive docstrings and comments following best practices
4. **Validator Agent**: Identifies potential issues, syntax errors, and suggests improvements

## 🛠️ Installation

### Prerequisites

- Python 3.8+
- Virtual environment (recommended)

### Setup

1. **Clone or download the project files**

2. **Create and activate a virtual environment**:
   ```bash
   python3 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**:
   - Copy the `.env` file and add your API keys:
   ```bash
   GOOGLE_API_KEY=your_google_api_key_here
   TAVILY_API_KEY=your_tavily_api_key_here
   ```

## 🔧 Configuration

### API Keys Required

- **Google API Key**: For Gemini 2.5 Flash model access
- **Tavily API Key**: For web search functionality during library research

### Prompts Configuration

The system uses YAML-based prompt configuration stored in `prompts.yaml`. You can customize the behavior of each agent by modifying their respective prompts:

```yaml
analyzer_prompt: |
  You are a Code Structure Analyzer. Your job is to:
  1. Analyze Python code structure
  2. Identify all imported libraries
  3. Assess current documentation quality
  4. Determine what libraries need research

  Be thorough but concise. Focus on factual analysis.
```

## 🚀 Usage

### Basic Usage

Run the main script to process the example code:

```bash
python main.py
```

### Custom Code Analysis

To analyze your own code, modify the `sample_code` variable in `main.py` or use the `write_code_to_file()` function:

```python
# Example: Save current script to a file
write_code_to_file("my_code.py")
```

### Workflow Visualization

The system automatically generates workflow diagrams in multiple formats:

- **PNG format**: `workflow_diagram.png` (if visualization libraries are available)
- **Mermaid format**: `workflow_diagram.mmd` (always generated as fallback)

## 📁 Project Structure

```
SelfDocumentation/
├── main.py              # Main application script
├── prompts.yaml         # Agent prompts configuration
├── requirements.txt     # Python dependencies
├── .env                # Environment variables (API keys)
├── README.md           # This file
├── workflow_diagram.mmd # Generated workflow diagram
└── venv/               # Virtual environment
```

## 🔍 Output

The system generates a comprehensive documentation report including:

- **Libraries Analysis**: Identified libraries and their usage
- **Documentation Quality Assessment**: Current state of code documentation
- **Enhanced Code**: Original code with added docstrings and comments
- **Issues Identification**: Potential problems and improvement suggestions
- **Sample Test Runs**: Example usage scenarios (when applicable)

## 🛠️ Dependencies

### Core Dependencies

- `langgraph`: Multi-agent workflow orchestration
- `langchain`: LLM framework and tools
- `langchain-google-genai`: Google Gemini integration
- `langchain-community`: Community tools and integrations
- `tavily-python`: Web search functionality
- `python-dotenv`: Environment variable management
- `PyYAML`: YAML configuration parsing

### Optional Dependencies

- `pyppeteer`: Local browser-based diagram rendering
- `graphviz`: Alternative diagram rendering

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📝 License

This project is open source and available under the MIT License.

## 🔧 Troubleshooting

### Common Issues

1. **API Key Errors**: Ensure your Google and Tavily API keys are correctly set in the `.env` file
2. **Visualization Issues**: If PNG generation fails, the system will fallback to Mermaid text format
3. **Import Errors**: Make sure all dependencies are installed in your virtual environment

### Support

For issues and questions, please check the documentation or create an issue in the project repository.

---

*Built with LangGraph and LangChain for intelligent code documentation automation.*
