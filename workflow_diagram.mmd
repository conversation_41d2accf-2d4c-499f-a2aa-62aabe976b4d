---
config:
  flowchart:
    curve: linear
---
graph TD;
	__start__([<p>__start__</p>]):::first
	analyze(analyze)
	research(research)
	document(document)
	validate(validate)
	output(output)
	__end__([<p>__end__</p>]):::last
	__start__ --> analyze;
	analyze --> research;
	document --> validate;
	research --> document;
	validate --> output;
	output --> __end__;
	classDef default fill:#f2f0ff,line-height:1.2
	classDef first fill-opacity:0
	classDef last fill:#bfb6fc
