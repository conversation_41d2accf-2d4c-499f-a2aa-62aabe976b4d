#!/usr/bin/env python3
"""
Script to create a PNG version of the workflow diagram using Mermaid.ink API
"""

import requests
import base64
import urllib.parse

def mermaid_to_png(mermaid_text, output_file="workflow_diagram.png"):
    """
    Convert Mermaid diagram text to PNG using Mermaid.ink API
    
    Args:
        mermaid_text (str): The Mermaid diagram text
        output_file (str): Output PNG file path
    """
    try:
        # Encode the Mermaid text for URL
        encoded = base64.b64encode(mermaid_text.encode('utf-8')).decode('ascii')
        
        # Create the Mermaid.ink URL
        url = f"https://mermaid.ink/img/{encoded}"
        
        print(f"Requesting PNG from: {url}")
        
        # Make the request
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        # Save the PNG
        with open(output_file, 'wb') as f:
            f.write(response.content)
        
        print(f"Successfully created {output_file}")
        return True
        
    except Exception as e:
        print(f"Failed to create PNG: {e}")
        return False

def main():
    """Main function to read Mermaid file and create PNG"""
    try:
        # Read the Mermaid file
        with open('workflow_diagram.mmd', 'r') as f:
            mermaid_content = f.read()
        
        # Create PNG
        success = mermaid_to_png(mermaid_content)
        
        if success:
            print("Workflow diagram PNG created successfully!")
        else:
            print("Failed to create PNG. Using Mermaid text format in README.")
            
    except FileNotFoundError:
        print("workflow_diagram.mmd not found. Please run main.py first.")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
